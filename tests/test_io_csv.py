"""Tests for io_csv module."""

import pytest
import pandas as pd
import tempfile
import os
from src.proj.io_csv import load_csv, normalize_header


def test_normalize_header():
    """Test header normalization."""
    assert normalize_header("Player Name") == "player_name"
    assert normalize_header("Passing Yds/G") == "passing_yds_g"
    assert normalize_header("Team-Defense") == "team_defense"
    assert normalize_header("  Extra  Spaces  ") == "extra_spaces"
    assert normalize_header("UPPERCASE") == "uppercase"
    assert normalize_header("mixed_Case123") == "mixed_case123"


def test_load_csv_basic():
    """Test basic CSV loading functionality."""
    # Create a temporary CSV file
    csv_content = """Player Name,Team,Passing Yds/G,Unnamed: 3
<PERSON>,<PERSON>,250.5,
<PERSON>,Team B,300.2,
<PERSON>,Team C,275.8,"""
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        f.write(csv_content)
        temp_path = f.name
    
    try:
        df, schema = load_csv(temp_path)
        
        # Check that Unnamed column was dropped
        assert 'unnamed_3' not in df.columns
        
        # Check header normalization
        expected_columns = ['player_name', 'team', 'passing_yds_g']
        assert list(df.columns) == expected_columns
        
        # Check schema structure
        assert 'columns' in schema
        assert 'dtypes' in schema
        assert 'shape' in schema
        assert 'null_counts' in schema
        
        # Check data integrity
        assert len(df) == 3
        assert df.iloc[0]['player_name'] == 'John Doe'
        
    finally:
        os.unlink(temp_path)


def test_load_csv_different_delimiters():
    """Test CSV loading with different delimiters."""
    # Test semicolon delimiter
    csv_content = "Name;Team;Score\nPlayer1;TeamA;100\nPlayer2;TeamB;200"
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        f.write(csv_content)
        temp_path = f.name
    
    try:
        df, schema = load_csv(temp_path)
        
        assert len(df.columns) == 3
        assert list(df.columns) == ['name', 'team', 'score']
        assert len(df) == 2
        
    finally:
        os.unlink(temp_path)


def test_load_csv_tab_delimiter():
    """Test CSV loading with tab delimiter."""
    csv_content = "Name\tTeam\tScore\nPlayer1\tTeamA\t100\nPlayer2\tTeamB\t200"
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        f.write(csv_content)
        temp_path = f.name
    
    try:
        df, schema = load_csv(temp_path)
        
        assert len(df.columns) == 3
        assert list(df.columns) == ['name', 'team', 'score']
        assert len(df) == 2
        
    finally:
        os.unlink(temp_path)
