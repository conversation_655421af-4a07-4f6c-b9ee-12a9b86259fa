"""Tests for fetch_odds module."""

import pytest
import pandas as pd
from src.proj.fetch_odds import implied_team_totals


def test_implied_team_totals():
    """Test implied team totals calculation."""
    # Mock odds data structure
    mock_odds_data = [
        {
            'id': 'game1',
            'sport_key': 'americanfootball_nfl',
            'sport_title': 'NFL',
            'commence_time': '2024-01-01T18:00:00Z',
            'home_team': 'Kansas City Chiefs',
            'away_team': 'Buffalo Bills',
            'bookmakers': [
                {
                    'key': 'draftkings',
                    'title': 'DraftKings',
                    'markets': [
                        {
                            'key': 'spreads',
                            'outcomes': [
                                {
                                    'name': 'Kansas City Chiefs',
                                    'price': -110,
                                    'point': -3.0
                                },
                                {
                                    'name': 'Buffalo Bills',
                                    'price': -110,
                                    'point': 3.0
                                }
                            ]
                        },
                        {
                            'key': 'totals',
                            'outcomes': [
                                {
                                    'name': 'Over',
                                    'price': -110,
                                    'point': 50.5
                                },
                                {
                                    'name': 'Under',
                                    'price': -110,
                                    'point': 50.5
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ]
    
    df = implied_team_totals(mock_odds_data)
    
    # Check DataFrame structure
    expected_columns = ['home_team', 'away_team', 'commence_time', 'spread', 'total', 
                       'home_implied_total', 'away_implied_total']
    assert all(col in df.columns for col in expected_columns)
    
    # Check data
    assert len(df) == 1
    row = df.iloc[0]
    
    assert row['home_team'] == 'Kansas City Chiefs'
    assert row['away_team'] == 'Buffalo Bills'
    assert row['spread'] == -3.0  # Chiefs favored by 3
    assert row['total'] == 50.5
    
    # Check implied totals calculation
    # Total = 50.5, Spread = -3.0 (Chiefs favored)
    # Home implied = 50.5/2 + (-3.0)/2 = 25.25 - 1.5 = 23.75
    # Away implied = 50.5/2 - (-3.0)/2 = 25.25 + 1.5 = 26.75
    assert abs(row['home_implied_total'] - 23.75) < 0.01
    assert abs(row['away_implied_total'] - 26.75) < 0.01


def test_implied_team_totals_empty():
    """Test implied team totals with empty data."""
    df = implied_team_totals([])
    
    expected_columns = ['home_team', 'away_team', 'commence_time', 'spread', 'total', 
                       'home_implied_total', 'away_implied_total']
    assert all(col in df.columns for col in expected_columns)
    assert len(df) == 0


def test_implied_team_totals_missing_odds():
    """Test implied team totals with missing odds data."""
    mock_odds_data = [
        {
            'id': 'game1',
            'sport_key': 'americanfootball_nfl',
            'sport_title': 'NFL',
            'commence_time': '2024-01-01T18:00:00Z',
            'home_team': 'Team A',
            'away_team': 'Team B',
            'bookmakers': []  # No bookmakers
        }
    ]
    
    df = implied_team_totals(mock_odds_data)
    
    assert len(df) == 1
    row = df.iloc[0]
    
    assert row['home_team'] == 'Team A'
    assert row['away_team'] == 'Team B'
    assert pd.isna(row['spread'])
    assert pd.isna(row['total'])
    assert pd.isna(row['home_implied_total'])
    assert pd.isna(row['away_implied_total'])
