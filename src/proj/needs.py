"""Needs detection system for identifying missing data and generating templates."""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple, Optional, Set
from dataclasses import dataclass
from pathlib import Path


@dataclass
class MissingDataItem:
    """Represents a missing data item."""
    category: str
    description: str
    player_name: Optional[str] = None
    team: Optional[str] = None
    game: Optional[str] = None
    priority: str = "medium"  # low, medium, high, critical
    template_needed: Optional[str] = None


class NeedsDetector:
    """Detects missing data and generates requirements."""
    
    def __init__(self):
        self.position_requirements = {
            'QB': {
                'critical': ['team_total', 'spread', 'pass_yds_team_prop'],
                'high': ['pressure_proxy', 'weather_conditions'],
                'medium': ['pace_factor', 'red_zone_efficiency'],
                'low': ['prime_time_flag', 'injury_status']
            },
            'RB': {
                'critical': ['team_rush_yds', 'role_notes'],
                'high': ['snap_share_expected', 'injury_status'],
                'medium': ['goal_line_role', 'pass_down_role'],
                'low': ['weather_conditions']
            },
            'WR': {
                'critical': ['target_share_prior', 'slot_outside_flag'],
                'high': ['snap_share_expected', 'role_notes'],
                'medium': ['adot_tendency', 'red_zone_role'],
                'low': ['injury_status', 'matchup_rating']
            },
            'TE': {
                'critical': ['target_share_prior', 'role_notes'],
                'high': ['snap_share_expected', 'blocking_role_pct'],
                'medium': ['red_zone_role'],
                'low': ['injury_status']
            },
            'DST': {
                'critical': ['team_sacks', 'opponent_qb_sacks_taken'],
                'high': ['turnover_rate', 'return_td_rate'],
                'medium': ['defensive_pace_factor'],
                'low': ['weather_conditions']
            }
        }
        
        self.game_level_requirements = {
            'critical': ['spread', 'total', 'weather_conditions'],
            'high': ['pace_factors', 'injury_report'],
            'medium': ['referee_crew', 'rest_days'],
            'low': ['prime_time_flag', 'divisional_game']
        }
    
    def detect_missing(self, players_df: pd.DataFrame, 
                      team_props_df: Optional[pd.DataFrame] = None,
                      player_props_df: Optional[pd.DataFrame] = None,
                      games_df: Optional[pd.DataFrame] = None,
                      roles_df: Optional[pd.DataFrame] = None) -> List[MissingDataItem]:
        """
        Detect missing data across all inputs.
        
        Args:
            players_df: Main player DataFrame
            team_props_df: Team props DataFrame
            player_props_df: Player props DataFrame  
            games_df: Games DataFrame
            roles_df: Player roles DataFrame
            
        Returns:
            List of MissingDataItem objects
        """
        missing_items = []
        
        # Check player-level requirements
        missing_items.extend(self._check_player_requirements(
            players_df, player_props_df, roles_df
        ))
        
        # Check team-level requirements
        missing_items.extend(self._check_team_requirements(
            players_df, team_props_df, games_df
        ))
        
        # Check game-level requirements
        missing_items.extend(self._check_game_requirements(
            players_df, games_df
        ))
        
        return missing_items
    
    def _check_player_requirements(self, players_df: pd.DataFrame,
                                 player_props_df: Optional[pd.DataFrame],
                                 roles_df: Optional[pd.DataFrame]) -> List[MissingDataItem]:
        """Check player-specific data requirements."""
        missing = []
        
        for _, player in players_df.iterrows():
            player_name = player['player_name']
            position = player['position']
            team = player['team']
            
            if position not in self.position_requirements:
                continue
                
            requirements = self.position_requirements[position]
            
            for priority, req_list in requirements.items():
                for requirement in req_list:
                    if not self._has_requirement(player, requirement, player_props_df, roles_df):
                        missing.append(MissingDataItem(
                            category="player_data",
                            description=f"Missing {requirement} for {player_name} ({position})",
                            player_name=player_name,
                            team=team,
                            priority=priority,
                            template_needed=self._get_template_for_requirement(requirement)
                        ))
        
        return missing
    
    def _check_team_requirements(self, players_df: pd.DataFrame,
                               team_props_df: Optional[pd.DataFrame],
                               games_df: Optional[pd.DataFrame]) -> List[MissingDataItem]:
        """Check team-level data requirements."""
        missing = []
        
        teams = players_df['team'].unique()
        
        for team in teams:
            # Check for team props
            if team_props_df is None or team_props_df.empty:
                missing.append(MissingDataItem(
                    category="team_props",
                    description=f"No team props data for {team}",
                    team=team,
                    priority="high",
                    template_needed="team_props_template.csv"
                ))
                continue
            
            team_props = team_props_df[team_props_df['team'] == team]
            
            # Check for specific team prop markets
            required_markets = ['team_total', 'pass_yds', 'rush_yds', 'team_sacks']
            available_markets = set(team_props['market'].unique()) if not team_props.empty else set()
            
            for market in required_markets:
                if market not in available_markets:
                    missing.append(MissingDataItem(
                        category="team_props",
                        description=f"Missing {market} team prop for {team}",
                        team=team,
                        priority="high" if market in ['team_total', 'pass_yds'] else "medium",
                        template_needed="team_props_template.csv"
                    ))
        
        return missing
    
    def _check_game_requirements(self, players_df: pd.DataFrame,
                               games_df: Optional[pd.DataFrame]) -> List[MissingDataItem]:
        """Check game-level data requirements."""
        missing = []
        
        if games_df is None or games_df.empty:
            missing.append(MissingDataItem(
                category="games",
                description="No games data provided",
                priority="critical",
                template_needed="games_template.csv"
            ))
            return missing
        
        # Check each game for required fields
        for _, game in games_df.iterrows():
            game_id = f"{game.get('home_team', 'UNK')}@{game.get('away_team', 'UNK')}"
            
            for priority, req_list in self.game_level_requirements.items():
                for requirement in req_list:
                    if requirement not in game or pd.isna(game[requirement]):
                        missing.append(MissingDataItem(
                            category="game_data",
                            description=f"Missing {requirement} for {game_id}",
                            game=game_id,
                            priority=priority,
                            template_needed="games_template.csv"
                        ))
        
        return missing
    
    def _has_requirement(self, player: pd.Series, requirement: str,
                        player_props_df: Optional[pd.DataFrame],
                        roles_df: Optional[pd.DataFrame]) -> bool:
        """Check if a player has a specific requirement met."""
        player_name = player['player_name']
        
        # Check in main player DataFrame
        if requirement in player and not pd.isna(player[requirement]):
            return True
        
        # Check in player props
        if player_props_df is not None and not player_props_df.empty:
            player_props = player_props_df[player_props_df['player_name'] == player_name]
            if not player_props.empty:
                if requirement in ['target_share_prior', 'pass_yds_team_prop']:
                    return True
        
        # Check in roles DataFrame
        if roles_df is not None and not roles_df.empty:
            player_roles = roles_df[roles_df['player_name'] == player_name]
            if not player_roles.empty:
                role_requirements = ['role_notes', 'slot_outside_flag', 'snap_share_expected']
                if requirement in role_requirements:
                    return requirement in player_roles.columns and not pd.isna(player_roles.iloc[0][requirement])
        
        return False
    
    def _get_template_for_requirement(self, requirement: str) -> str:
        """Get the appropriate template file for a requirement."""
        template_mapping = {
            'target_share_prior': 'roles_notes_template.csv',
            'slot_outside_flag': 'roles_notes_template.csv',
            'role_notes': 'roles_notes_template.csv',
            'snap_share_expected': 'roles_notes_template.csv',
            'team_total': 'team_props_template.csv',
            'pass_yds_team_prop': 'team_props_template.csv',
            'team_rush_yds': 'team_props_template.csv',
            'team_sacks': 'team_props_template.csv',
            'spread': 'games_template.csv',
            'weather_conditions': 'games_template.csv'
        }
        
        return template_mapping.get(requirement, 'player_props_template.csv')
    
    def confidence_score(self, player: pd.Series, missing_items: List[MissingDataItem]) -> float:
        """
        Calculate confidence score for a player (0-1).
        
        Args:
            player: Player row from DataFrame
            missing_items: List of missing items for this player
            
        Returns:
            Confidence score from 0 (no confidence) to 1 (full confidence)
        """
        player_name = player['player_name']
        position = player['position']
        
        if position not in self.position_requirements:
            return 0.5  # Default for unknown positions
        
        # Get player-specific missing items
        player_missing = [item for item in missing_items 
                         if item.player_name == player_name]
        
        # Weight by priority
        priority_weights = {'critical': 0.4, 'high': 0.3, 'medium': 0.2, 'low': 0.1}
        total_possible_weight = sum(
            priority_weights[priority] * len(req_list)
            for priority, req_list in self.position_requirements[position].items()
        )
        
        missing_weight = sum(
            priority_weights[item.priority] for item in player_missing
        )
        
        if total_possible_weight == 0:
            return 1.0
        
        confidence = 1.0 - (missing_weight / total_possible_weight)
        return max(0.0, min(1.0, confidence))
    
    def generate_templates(self, output_dir: str = "templates") -> Dict[str, str]:
        """
        Generate CSV templates for missing data.
        
        Args:
            output_dir: Directory to save templates
            
        Returns:
            Dictionary mapping template names to file paths
        """
        Path(output_dir).mkdir(exist_ok=True)
        
        templates = {
            'player_props_template.csv': [
                'player_name', 'team', 'opponent', 'position', 'market', 
                'line', 'over_odds', 'under_odds', 'book', 'timestamp_iso'
            ],
            'team_props_template.csv': [
                'team', 'opponent', 'market', 'line', 'over_odds', 
                'under_odds', 'book', 'timestamp_iso'
            ],
            'roles_notes_template.csv': [
                'player_name', 'team', 'position', 'expected_snap_pct',
                'role_notes', 'slot_outside_flag', 'target_share_prior'
            ],
            'games_template.csv': [
                'home_team', 'away_team', 'spread', 'total', 'weather_conditions',
                'pace_factors', 'injury_report', 'referee_crew', 'rest_days',
                'prime_time_flag', 'divisional_game'
            ]
        }
        
        created_files = {}
        
        for filename, columns in templates.items():
            filepath = Path(output_dir) / filename
            
            # Create empty CSV with headers
            pd.DataFrame(columns=columns).to_csv(filepath, index=False)
            created_files[filename] = str(filepath)
        
        return created_files
    
    def generate_needs_report(self, missing_items: List[MissingDataItem],
                            players_df: pd.DataFrame) -> str:
        """
        Generate a human-readable needs report.
        
        Args:
            missing_items: List of missing data items
            players_df: Player DataFrame for confidence scoring
            
        Returns:
            Formatted report string
        """
        report_lines = ["# NFL Projections - Missing Data Report\n"]
        
        # Summary
        total_items = len(missing_items)
        critical_items = len([item for item in missing_items if item.priority == "critical"])
        high_items = len([item for item in missing_items if item.priority == "high"])
        
        report_lines.append(f"## Summary")
        report_lines.append(f"- Total missing items: {total_items}")
        report_lines.append(f"- Critical items: {critical_items}")
        report_lines.append(f"- High priority items: {high_items}")
        report_lines.append("")
        
        # Group by category and priority
        categories = {}
        for item in missing_items:
            if item.category not in categories:
                categories[item.category] = {'critical': [], 'high': [], 'medium': [], 'low': []}
            categories[item.category][item.priority].append(item)
        
        # Generate sections
        for category, priority_items in categories.items():
            report_lines.append(f"## {category.replace('_', ' ').title()}")
            
            for priority in ['critical', 'high', 'medium', 'low']:
                items = priority_items[priority]
                if items:
                    report_lines.append(f"### {priority.title()} Priority")
                    for item in items:
                        report_lines.append(f"- {item.description}")
                        if item.template_needed:
                            report_lines.append(f"  → Template: {item.template_needed}")
                    report_lines.append("")
        
        # Confidence scores for lowest confidence players
        report_lines.append("## Lowest Confidence Players")
        player_confidences = []
        
        for _, player in players_df.iterrows():
            confidence = self.confidence_score(player, missing_items)
            player_confidences.append((player['player_name'], player['position'], confidence))
        
        # Sort by confidence (lowest first)
        player_confidences.sort(key=lambda x: x[2])
        
        for name, position, confidence in player_confidences[:10]:
            report_lines.append(f"- {name} ({position}): {confidence:.1%} confidence")
        
        return "\n".join(report_lines)
