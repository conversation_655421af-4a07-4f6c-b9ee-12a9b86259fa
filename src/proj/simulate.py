"""NFL game simulation utilities."""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional


def simulate_game(home_total: float, away_total: float, 
                 home_std: float = 7.0, away_std: float = 7.0,
                 n_simulations: int = 10000) -> Dict[str, float]:
    """
    Simulate NFL game outcomes.
    
    Args:
        home_total: Expected points for home team
        away_total: Expected points for away team  
        home_std: Standard deviation for home team scoring
        away_std: Standard deviation for away team scoring
        n_simulations: Number of simulations to run
        
    Returns:
        Dictionary with simulation results
    """
    # Generate random scores
    home_scores = np.random.normal(home_total, home_std, n_simulations)
    away_scores = np.random.normal(away_total, away_std, n_simulations)
    
    # Ensure non-negative scores
    home_scores = np.maximum(home_scores, 0)
    away_scores = np.maximum(away_scores, 0)
    
    # Calculate outcomes
    home_wins = np.sum(home_scores > away_scores)
    away_wins = np.sum(away_scores > home_scores)
    ties = n_simulations - home_wins - away_wins
    
    total_points = home_scores + away_scores
    
    results = {
        'home_win_prob': home_wins / n_simulations,
        'away_win_prob': away_wins / n_simulations,
        'tie_prob': ties / n_simulations,
        'avg_home_score': np.mean(home_scores),
        'avg_away_score': np.mean(away_scores),
        'avg_total_points': np.mean(total_points),
        'over_under_line': home_total + away_total,
        'over_prob': np.sum(total_points > (home_total + away_total)) / n_simulations
    }
    
    return results


def simulate_season(games_df: pd.DataFrame, n_simulations: int = 1000) -> pd.DataFrame:
    """
    Simulate multiple games for season projections.
    
    Args:
        games_df: DataFrame with columns home_team, away_team, home_implied_total, away_implied_total
        n_simulations: Number of simulations per game
        
    Returns:
        DataFrame with simulation results for each game
    """
    results = []
    
    for _, game in games_df.iterrows():
        sim_result = simulate_game(
            game['home_implied_total'],
            game['away_implied_total'],
            n_simulations=n_simulations
        )
        
        game_result = {
            'home_team': game['home_team'],
            'away_team': game['away_team'],
            **sim_result
        }
        
        results.append(game_result)
    
    return pd.DataFrame(results)


def calculate_team_records(simulation_results: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate projected team records from simulation results.
    
    Args:
        simulation_results: DataFrame from simulate_season
        
    Returns:
        DataFrame with team records
    """
    team_stats = {}
    
    for _, game in simulation_results.iterrows():
        home_team = game['home_team']
        away_team = game['away_team']
        
        # Initialize teams if not seen before
        for team in [home_team, away_team]:
            if team not in team_stats:
                team_stats[team] = {
                    'games': 0,
                    'expected_wins': 0.0,
                    'expected_losses': 0.0,
                    'expected_points_for': 0.0,
                    'expected_points_against': 0.0
                }
        
        # Update home team stats
        team_stats[home_team]['games'] += 1
        team_stats[home_team]['expected_wins'] += game['home_win_prob']
        team_stats[home_team]['expected_losses'] += game['away_win_prob']
        team_stats[home_team]['expected_points_for'] += game['avg_home_score']
        team_stats[home_team]['expected_points_against'] += game['avg_away_score']
        
        # Update away team stats
        team_stats[away_team]['games'] += 1
        team_stats[away_team]['expected_wins'] += game['away_win_prob']
        team_stats[away_team]['expected_losses'] += game['home_win_prob']
        team_stats[away_team]['expected_points_for'] += game['avg_away_score']
        team_stats[away_team]['expected_points_against'] += game['avg_home_score']
    
    # Convert to DataFrame
    records_df = pd.DataFrame.from_dict(team_stats, orient='index')
    records_df.index.name = 'team'
    records_df = records_df.reset_index()
    
    # Calculate additional metrics
    records_df['win_percentage'] = records_df['expected_wins'] / records_df['games']
    records_df['point_differential'] = (records_df['expected_points_for'] - 
                                       records_df['expected_points_against'])
    
    # Sort by expected wins
    records_df = records_df.sort_values('expected_wins', ascending=False)
    
    return records_df
