"""Fetch NFL odds from The Odds API."""

import requests
import pandas as pd
from typing import Dict, Any, List


def get_totals_spreads(api_key: str, sport: str = "americanfootball_nfl") -> Dict[str, Any]:
    """
    Fetch NFL spreads and totals from The Odds API.
    
    Args:
        api_key: The Odds API key
        sport: Sport identifier (default: americanfootball_nfl)
        
    Returns:
        Dictionary containing odds data
    """
    url = f"https://api.the-odds-api.com/v4/sports/{sport}/odds"
    
    params = {
        'api_key': api_key,
        'regions': 'us',
        'markets': 'spreads,totals',
        'oddsFormat': 'american',
        'dateFormat': 'iso'
    }
    
    response = requests.get(url, params=params)
    response.raise_for_status()
    
    return response.json()


def implied_team_totals(odds_data: Dict[str, Any]) -> pd.DataFrame:
    """
    Convert odds data to DataFrame with implied team totals.
    
    Args:
        odds_data: Raw odds data from The Odds API
        
    Returns:
        DataFrame with columns: home_team, away_team, spread, total, 
                               home_implied_total, away_implied_total
    """
    games = []
    
    for game in odds_data:
        home_team = game['home_team']
        away_team = game['away_team']
        commence_time = game['commence_time']
        
        # Initialize game data
        game_data = {
            'home_team': home_team,
            'away_team': away_team,
            'commence_time': commence_time,
            'spread': None,
            'total': None,
            'home_implied_total': None,
            'away_implied_total': None
        }
        
        # Extract spreads and totals from bookmakers
        if 'bookmakers' in game and game['bookmakers']:
            bookmaker = game['bookmakers'][0]  # Use first bookmaker
            
            for market in bookmaker.get('markets', []):
                if market['key'] == 'spreads':
                    # Find home team spread
                    for outcome in market['outcomes']:
                        if outcome['name'] == home_team:
                            game_data['spread'] = outcome['point']
                            break
                
                elif market['key'] == 'totals':
                    # Get the total points
                    for outcome in market['outcomes']:
                        if outcome['name'] == 'Over':
                            game_data['total'] = outcome['point']
                            break
        
        # Calculate implied team totals
        if game_data['spread'] is not None and game_data['total'] is not None:
            total = game_data['total']
            spread = game_data['spread']  # Positive means home team is favored
            
            # Implied totals: total/2 +/- spread/2
            game_data['home_implied_total'] = total / 2 + spread / 2
            game_data['away_implied_total'] = total / 2 - spread / 2
        
        games.append(game_data)
    
    df = pd.DataFrame(games)

    # Ensure all expected columns exist even for empty DataFrame
    expected_columns = ['home_team', 'away_team', 'commence_time', 'spread', 'total',
                       'home_implied_total', 'away_implied_total']
    for col in expected_columns:
        if col not in df.columns:
            df[col] = None

    return df[expected_columns]
