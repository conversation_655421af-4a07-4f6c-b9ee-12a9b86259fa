"""Hierarchical priors for NFL projections."""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass


@dataclass
class PlayerPrior:
    """Player-specific prior information."""
    player_id: str
    position: str
    team: str
    mu_base: float
    sigma_base: float
    uncertainty_multiplier: float = 1.0
    tags: List[str] = None
    
    def __post_init__(self):
        if self.tags is None:
            self.tags = []


class HierarchicalPriors:
    """Hierarchical prior system for NFL projections."""
    
    def __init__(self):
        self.position_baselines = {}
        self.team_adjustments = {}
        self.coach_tendencies = {}
        self.week1_multipliers = {
            'rookie': 2.0,
            'new_team': 1.5,
            'new_oc': 1.3,
            'new_qb': 1.4,
            'committee': 1.6,
            'stable_vet': 1.0
        }
        self.league_position_baselines = self._get_default_position_baselines()
    
    def _get_default_position_baselines(self) -> Dict[str, Dict[str, float]]:
        """Get default position baselines for DraftKings scoring."""
        return {
            'QB': {'mu': 18.5, 'sigma': 6.5},
            'RB': {'mu': 12.8, 'sigma': 7.2},
            'WR': {'mu': 11.2, 'sigma': 6.8},
            'TE': {'mu': 8.9, 'sigma': 5.4},
            'DST': {'mu': 7.5, 'sigma': 4.8}
        }
    
    def load_player_history(self, history_df: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """
        Load player historical performance data.
        
        Args:
            history_df: DataFrame with columns [player_id, position, games, avg_points, std_points]
            
        Returns:
            Dictionary mapping player_id to {mu, sigma, games}
        """
        player_history = {}
        
        for _, row in history_df.iterrows():
            player_id = row['player_id']
            
            # Weight by number of games (more games = more confidence)
            games = row.get('games', 1)
            confidence_weight = min(games / 16.0, 1.0)  # Cap at 1 season
            
            # Adjust sigma based on sample size
            base_sigma = row.get('std_points', 6.0)
            adjusted_sigma = base_sigma / np.sqrt(max(games, 1))
            
            player_history[player_id] = {
                'mu': row.get('avg_points', 0),
                'sigma': adjusted_sigma,
                'games': games,
                'confidence': confidence_weight
            }
        
        return player_history
    
    def load_team_tendencies(self, team_data: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """
        Load team-specific tendencies and adjustments.
        
        Args:
            team_data: DataFrame with team tendency data
            
        Returns:
            Dictionary of team adjustments
        """
        team_tendencies = {}
        
        for _, row in team_data.iterrows():
            team = row['team']
            team_tendencies[team] = {
                'pace_factor': row.get('pace_factor', 1.0),
                'pass_rate': row.get('pass_rate', 0.6),
                'red_zone_efficiency': row.get('rz_efficiency', 0.55),
                'target_concentration': row.get('target_concentration', 0.3),
                'rb_committee_factor': row.get('rb_committee', 1.0)
            }
        
        return team_tendencies
    
    def load_coach_tendencies(self, coach_data: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """Load coaching tendencies that affect player usage."""
        coach_tendencies = {}
        
        for _, row in coach_data.iterrows():
            coach = row['coach_name']
            coach_tendencies[coach] = {
                'aggression_factor': row.get('aggression', 1.0),
                'wr_target_distribution': row.get('wr_distribution', 0.5),
                'rb_usage_variance': row.get('rb_variance', 1.0),
                'te_involvement': row.get('te_factor', 1.0)
            }
        
        return coach_tendencies
    
    def get_uncertainty_multiplier(self, player_tags: List[str], week: int = 1) -> float:
        """
        Calculate uncertainty multiplier based on player situation.
        
        Args:
            player_tags: List of tags describing player situation
            week: NFL week number
            
        Returns:
            Multiplier for sigma (>1 = more uncertain)
        """
        if week != 1:
            return 1.0
        
        multiplier = 1.0
        
        for tag in player_tags:
            if tag in self.week1_multipliers:
                # Use max multiplier if multiple tags apply
                multiplier = max(multiplier, self.week1_multipliers[tag])
        
        return multiplier
    
    def build_player_prior(self, player_id: str, position: str, team: str,
                          player_history: Optional[Dict] = None,
                          player_tags: Optional[List[str]] = None,
                          week: int = 1) -> PlayerPrior:
        """
        Build hierarchical prior for a player.
        
        Args:
            player_id: Unique player identifier
            position: Player position
            team: Player team
            player_history: Historical performance data
            player_tags: Situation tags (rookie, new_team, etc.)
            week: NFL week number
            
        Returns:
            PlayerPrior object
        """
        if player_tags is None:
            player_tags = []
        
        # Start with league position baseline
        position_baseline = self.league_position_baselines.get(position, {'mu': 10.0, 'sigma': 6.0})
        mu_base = position_baseline['mu']
        sigma_base = position_baseline['sigma']
        
        # Adjust for team tendencies
        if team in self.team_adjustments:
            team_adj = self.team_adjustments[team]
            mu_base *= team_adj.get('scoring_factor', 1.0)
            sigma_base *= team_adj.get('variance_factor', 1.0)
        
        # Incorporate player history if available
        if player_history and player_id in player_history:
            hist = player_history[player_id]
            confidence = hist['confidence']
            
            # Blend league baseline with player history
            # Higher confidence = more weight on player history
            mu_base = (1 - confidence) * mu_base + confidence * hist['mu']
            sigma_base = (1 - confidence) * sigma_base + confidence * hist['sigma']
        
        # Apply uncertainty multiplier for Week 1 situations
        uncertainty_mult = self.get_uncertainty_multiplier(player_tags, week)
        
        return PlayerPrior(
            player_id=player_id,
            position=position,
            team=team,
            mu_base=mu_base,
            sigma_base=sigma_base,
            uncertainty_multiplier=uncertainty_mult,
            tags=player_tags
        )
    
    def build_priors_for_slate(self, players_df: pd.DataFrame,
                              player_history: Optional[Dict] = None,
                              week: int = 1) -> Dict[str, PlayerPrior]:
        """
        Build priors for all players in a slate.
        
        Args:
            players_df: DataFrame with player information
            player_history: Historical performance data
            week: NFL week number
            
        Returns:
            Dictionary mapping player_id to PlayerPrior
        """
        priors = {}
        
        for _, row in players_df.iterrows():
            player_id = row['player_id']
            position = row['position']
            team = row['team']
            
            # Extract tags from DataFrame if present
            tags = []
            if 'is_rookie' in row and row['is_rookie']:
                tags.append('rookie')
            if 'new_team' in row and row['new_team']:
                tags.append('new_team')
            if 'new_oc' in row and row['new_oc']:
                tags.append('new_oc')
            if 'committee_rb' in row and row['committee_rb']:
                tags.append('committee')
            if 'veteran_stable' in row and row['veteran_stable']:
                tags.append('stable_vet')
            
            prior = self.build_player_prior(
                player_id=player_id,
                position=position,
                team=team,
                player_history=player_history,
                player_tags=tags,
                week=week
            )
            
            priors[player_id] = prior
        
        return priors
    
    def get_adjusted_prior(self, prior: PlayerPrior) -> Tuple[float, float]:
        """
        Get final adjusted prior with uncertainty multiplier applied.
        
        Args:
            prior: PlayerPrior object
            
        Returns:
            Tuple of (mu_adjusted, sigma_adjusted)
        """
        mu_adjusted = prior.mu_base
        sigma_adjusted = prior.sigma_base * prior.uncertainty_multiplier
        
        return mu_adjusted, sigma_adjusted
