"""Command line interface for NFL projections."""

import argparse
import json
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

from .io_csv import load_csv
from .fetch_odds import get_totals_spreads, implied_team_totals


def cmd_load(args):
    """Load and analyze CSV file."""
    try:
        df, schema = load_csv(args.csv)
        
        print(f"Successfully loaded CSV: {args.csv}")
        print(f"Shape: {schema['shape']}")
        print("\nSchema:")
        print(f"Columns: {schema['columns']}")
        print("\nData types:")
        for col, dtype in schema['dtypes'].items():
            print(f"  {col}: {dtype}")
        
        print("\nNull counts:")
        for col, null_count in schema['null_counts'].items():
            if null_count > 0:
                print(f"  {col}: {null_count}")
        
        print(f"\nFirst 5 rows:")
        print(df.head().to_string())
        
    except Exception as e:
        print(f"Error loading CSV: {e}", file=sys.stderr)
        sys.exit(1)


def cmd_odds(args):
    """Fetch NFL odds and save to JSON."""
    load_dotenv()
    
    api_key = os.getenv('ODDS_API_KEY')
    if not api_key:
        print("Error: ODDS_API_KEY not found in environment variables", file=sys.stderr)
        print("Make sure to set it in your .env file", file=sys.stderr)
        sys.exit(1)
    
    try:
        print("Fetching NFL odds...")
        odds_data = get_totals_spreads(api_key)
        
        # Create output directory if it doesn't exist
        output_path = Path(args.out)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Save raw odds data
        with open(output_path, 'w') as f:
            json.dump(odds_data, f, indent=2)
        
        print(f"Odds data saved to: {output_path}")
        
        # Also create and display implied totals
        df_totals = implied_team_totals(odds_data)
        print(f"\nFound {len(df_totals)} games with odds")
        
        if not df_totals.empty:
            print("\nImplied team totals:")
            print(df_totals[['home_team', 'away_team', 'spread', 'total', 
                           'home_implied_total', 'away_implied_total']].to_string(index=False))
        
    except Exception as e:
        print(f"Error fetching odds: {e}", file=sys.stderr)
        sys.exit(1)


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(description='NFL Projections CLI')
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Load command
    load_parser = subparsers.add_parser('load', help='Load and analyze CSV file')
    load_parser.add_argument('--csv', required=True, help='Path to CSV file')
    load_parser.set_defaults(func=cmd_load)
    
    # Odds command
    odds_parser = subparsers.add_parser('odds', help='Fetch NFL odds')
    odds_parser.add_argument('--out', default='data/odds_week1.json', 
                           help='Output path for odds JSON file')
    odds_parser.set_defaults(func=cmd_odds)
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        sys.exit(1)
    
    args.func(args)


if __name__ == '__main__':
    main()
