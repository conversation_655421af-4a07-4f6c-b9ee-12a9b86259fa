"""NFL projection models."""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional


class NFLProjectionModel:
    """Base class for NFL projection models."""
    
    def __init__(self):
        self.is_fitted = False
        self.features = []
    
    def fit(self, X: pd.DataFrame, y: pd.Series) -> 'NFLProjectionModel':
        """Fit the model to training data."""
        raise NotImplementedError
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Make predictions on new data."""
        raise NotImplementedError
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance scores."""
        raise NotImplementedError


class SimpleLinearModel(NFLProjectionModel):
    """Simple linear regression model for NFL projections."""
    
    def __init__(self):
        super().__init__()
        self.coefficients = {}
        self.intercept = 0.0
    
    def fit(self, X: pd.DataFrame, y: pd.Series) -> 'SimpleLinearModel':
        """Fit linear model using normal equation."""
        from scipy.linalg import lstsq
        
        self.features = list(X.columns)
        
        # Add intercept column
        X_with_intercept = np.column_stack([np.ones(len(X)), X.values])
        
        # Solve normal equation
        coeffs, _, _, _ = lstsq(X_with_intercept, y.values)
        
        self.intercept = coeffs[0]
        self.coefficients = dict(zip(self.features, coeffs[1:]))
        self.is_fitted = True
        
        return self
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Make predictions."""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")
        
        predictions = np.full(len(X), self.intercept)
        for feature in self.features:
            if feature in X.columns:
                predictions += X[feature].values * self.coefficients[feature]
        
        return predictions
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Get coefficient magnitudes as feature importance."""
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")
        
        return {feature: abs(coeff) for feature, coeff in self.coefficients.items()}
