"""Generic CSV loader with auto-detection and normalization."""

import pandas as pd
import re
from typing import <PERSON><PERSON>, Dict, Any


def normalize_header(header: str) -> str:
    """Convert header to snake_case."""
    # Remove special characters and replace with underscores
    header = re.sub(r'[^\w\s]', '_', header)
    # Replace spaces with underscores
    header = re.sub(r'\s+', '_', header)
    # Convert to lowercase
    header = header.lower()
    # Remove multiple consecutive underscores
    header = re.sub(r'_+', '_', header)
    # Remove leading/trailing underscores
    header = header.strip('_')
    return header


def load_csv(path: str) -> Tuple[pd.DataFrame, Dict[str, Any]]:
    """
    Load CSV with auto-detection and normalization.
    
    Args:
        path: Path to CSV file
        
    Returns:
        Tuple of (DataFrame, schema_dict)
        schema_dict contains 'columns' and 'dtypes'
    """
    # Try different delimiters
    delimiters = [',', ';', '\t', '|']
    df = None
    
    for delimiter in delimiters:
        try:
            temp_df = pd.read_csv(path, delimiter=delimiter, nrows=5)
            # Check if we have more than one column (good delimiter detection)
            if len(temp_df.columns) > 1:
                df = pd.read_csv(path, delimiter=delimiter)
                break
        except Exception:
            continue
    
    if df is None:
        # Fallback to default comma delimiter
        df = pd.read_csv(path)
    
    # Drop empty Unnamed columns
    unnamed_cols = [col for col in df.columns if col.startswith('Unnamed:')]
    if unnamed_cols:
        df = df.drop(columns=unnamed_cols)
    
    # Normalize headers to snake_case
    df.columns = [normalize_header(col) for col in df.columns]
    
    # Create schema dictionary
    schema = {
        'columns': list(df.columns),
        'dtypes': {col: str(dtype) for col, dtype in df.dtypes.items()},
        'shape': df.shape,
        'null_counts': df.isnull().sum().to_dict()
    }
    
    return df, schema
