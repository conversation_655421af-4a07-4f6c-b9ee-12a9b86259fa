"""Team and player data reconciliation utilities."""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from scipy.optimize import minimize
from .model import american_to_prob, devig, line_to_implied_mean


def map_team_props_to_games(team_props_df: pd.DataFrame, 
                           games_df: pd.DataFrame) -> pd.DataFrame:
    """
    Map team props to specific games and compute implied team stats.
    
    Args:
        team_props_df: DataFrame with team prop lines
        games_df: DataFrame with game information
        
    Returns:
        DataFrame with team-level implied stats per game
    """
    team_stats = []
    
    for _, game in games_df.iterrows():
        home_team = game['home_team']
        away_team = game['away_team']
        
        for team in [home_team, away_team]:
            opponent = away_team if team == home_team else home_team
            
            # Find team props for this team in this game
            team_props = team_props_df[
                (team_props_df['team'] == team) & 
                (team_props_df['opponent'] == opponent)
            ]
            
            game_stats = {
                'game_id': f"{home_team}@{away_team}",
                'team': team,
                'opponent': opponent,
                'is_home': team == home_team
            }
            
            # Process each market
            for _, prop in team_props.iterrows():
                market = prop['market']
                line = prop['line']
                over_odds = prop['over_odds']
                under_odds = prop['under_odds']
                
                # Convert to implied mean
                p_over_raw = american_to_prob(over_odds)
                p_under_raw = american_to_prob(under_odds)
                p_over_fair, _ = devig(p_over_raw, p_under_raw)
                
                # Estimate sigma based on market type
                sigma_estimates = {
                    'team_total': 7.0,
                    'pass_yds': 25.0,
                    'rush_yds': 20.0,
                    'team_sacks': 1.2,
                    'turnovers': 0.8,
                    'first_downs': 2.5
                }
                
                sigma = sigma_estimates.get(market, 5.0)
                implied_mean = line_to_implied_mean(line, p_over_fair, sigma)
                
                game_stats[f'{market}_implied'] = implied_mean
                game_stats[f'{market}_line'] = line
                game_stats[f'{market}_sigma'] = sigma
            
            team_stats.append(game_stats)
    
    return pd.DataFrame(team_stats)


def reconcile_player_team_totals(player_projections: pd.DataFrame,
                                team_stats: pd.DataFrame,
                                tolerance: float = 0.1) -> pd.DataFrame:
    """
    Reconcile player projections with team-level constraints.
    
    Args:
        player_projections: DataFrame with player projections
        team_stats: DataFrame with team-level implied stats
        tolerance: Allowed deviation from team totals (as fraction)
        
    Returns:
        Adjusted player projections DataFrame
    """
    adjusted_projections = player_projections.copy()
    
    # Group by team and game
    for (team, game_id), team_group in player_projections.groupby(['team', 'game_id']):
        # Get team constraints for this game
        team_constraint = team_stats[
            (team_stats['team'] == team) & 
            (team_stats['game_id'] == game_id)
        ]
        
        if team_constraint.empty:
            continue
            
        team_row = team_constraint.iloc[0]
        
        # Reconcile passing yards
        if 'pass_yds_implied' in team_row:
            qb_players = team_group[team_group['position'] == 'QB']
            if not qb_players.empty:
                team_pass_target = team_row['pass_yds_implied']
                current_qb_total = qb_players['pass_yds_proj'].sum()
                
                if current_qb_total > 0:
                    adjustment_factor = team_pass_target / current_qb_total
                    # Apply soft constraint (don't force exact match)
                    soft_factor = 0.7 * adjustment_factor + 0.3 * 1.0
                    
                    for idx in qb_players.index:
                        adjusted_projections.loc[idx, 'pass_yds_proj'] *= soft_factor
        
        # Reconcile rushing yards
        if 'rush_yds_implied' in team_row:
            rb_players = team_group[team_group['position'].isin(['RB', 'QB'])]
            if not rb_players.empty:
                team_rush_target = team_row['rush_yds_implied']
                current_rush_total = rb_players['rush_yds_proj'].sum()
                
                if current_rush_total > 0:
                    adjustment_factor = team_rush_target / current_rush_total
                    soft_factor = 0.6 * adjustment_factor + 0.4 * 1.0
                    
                    for idx in rb_players.index:
                        adjusted_projections.loc[idx, 'rush_yds_proj'] *= soft_factor
        
        # Reconcile receiving yards (distribute among pass catchers)
        rec_players = team_group[team_group['position'].isin(['WR', 'TE', 'RB'])]
        if not rec_players.empty and 'pass_yds_implied' in team_row:
            # Estimate team receiving yards (typically ~85% of passing yards)
            team_rec_target = team_row['pass_yds_implied'] * 0.85
            current_rec_total = rec_players['rec_yds_proj'].sum()
            
            if current_rec_total > 0:
                adjustment_factor = team_rec_target / current_rec_total
                soft_factor = 0.5 * adjustment_factor + 0.5 * 1.0
                
                for idx in rec_players.index:
                    adjusted_projections.loc[idx, 'rec_yds_proj'] *= soft_factor
    
    return adjusted_projections


def apply_defensive_pressure_adjustments(player_projections: pd.DataFrame,
                                       team_stats: pd.DataFrame) -> pd.DataFrame:
    """
    Adjust QB projections based on defensive pressure metrics.
    
    Args:
        player_projections: DataFrame with player projections
        team_stats: DataFrame with team defensive stats
        
    Returns:
        Adjusted projections with pressure considerations
    """
    adjusted_projections = player_projections.copy()
    
    for idx, player in player_projections.iterrows():
        if player['position'] != 'QB':
            continue
            
        team = player['team']
        opponent = player['opponent']
        
        # Find opponent's defensive pressure stats
        opp_defense = team_stats[
            (team_stats['team'] == opponent) & 
            (team_stats['game_id'] == player['game_id'])
        ]
        
        # Find team's QB pressure allowed stats
        team_oline = team_stats[
            (team_stats['team'] == team) & 
            (team_stats['game_id'] == player['game_id'])
        ]
        
        pressure_adjustment = 1.0
        
        # Adjust based on opponent sacks
        if not opp_defense.empty and 'team_sacks_implied' in opp_defense.iloc[0]:
            opp_sacks = opp_defense.iloc[0]['team_sacks_implied']
            if opp_sacks > 3.0:  # High pressure defense
                pressure_adjustment *= 0.95  # Slight downward adjustment
            elif opp_sacks < 1.5:  # Low pressure defense
                pressure_adjustment *= 1.05  # Slight upward adjustment
        
        # Adjust based on team's sacks allowed (if available)
        if not team_oline.empty and 'qb_sacks_taken_implied' in team_oline.iloc[0]:
            sacks_allowed = team_oline.iloc[0]['qb_sacks_taken_implied']
            if sacks_allowed > 3.0:  # Poor pass protection
                pressure_adjustment *= 0.93
            elif sacks_allowed < 1.5:  # Good pass protection
                pressure_adjustment *= 1.07
        
        # Apply pressure adjustments to QB stats
        qb_stats = ['pass_yds_proj', 'pass_tds_proj', 'completions_proj']
        for stat in qb_stats:
            if stat in adjusted_projections.columns:
                adjusted_projections.loc[idx, stat] *= pressure_adjustment
        
        # Inverse adjustment for scrambling/rushing (more pressure = more scrambles)
        if 'rush_yds_proj' in adjusted_projections.columns:
            scramble_factor = 2.0 - pressure_adjustment  # Inverse relationship
            adjusted_projections.loc[idx, 'rush_yds_proj'] *= scramble_factor
        
        # Adjust target distribution (more checkdowns under pressure)
        if pressure_adjustment < 1.0:  # Under pressure
            # This would adjust TE/RB target shares upward
            # Implementation depends on how target shares are stored
            pass
    
    return adjusted_projections


def compute_target_share_constraints(team_stats: pd.DataFrame,
                                   player_roles: pd.DataFrame) -> Dict[str, Dict[str, float]]:
    """
    Compute target share constraints based on team passing volume and player roles.
    
    Args:
        team_stats: Team-level passing statistics
        player_roles: Player role information
        
    Returns:
        Dictionary with target share constraints by team/game
    """
    constraints = {}
    
    for _, team_row in team_stats.iterrows():
        if 'pass_yds_implied' not in team_row:
            continue
            
        team = team_row['team']
        game_id = team_row['game_id']
        
        # Estimate total targets from passing yards
        # Rule of thumb: ~6.5 yards per target in NFL
        estimated_targets = team_row['pass_yds_implied'] / 6.5
        
        # Get players for this team
        team_players = player_roles[player_roles['team'] == team]
        
        # Distribute targets based on roles and snap counts
        target_distribution = {}
        
        for _, player in team_players.iterrows():
            if player['position'] not in ['WR', 'TE', 'RB']:
                continue
                
            player_name = player['player_name']
            snap_pct = player.get('expected_snap_pct', 0.5)
            
            # Base target share by position and role
            if player['position'] == 'WR':
                if 'WR1' in player.get('role_notes', ''):
                    base_share = 0.25
                elif 'WR2' in player.get('role_notes', ''):
                    base_share = 0.18
                else:
                    base_share = 0.12
            elif player['position'] == 'TE':
                base_share = 0.15
            elif player['position'] == 'RB':
                base_share = 0.08
            else:
                base_share = 0.05
            
            # Adjust by snap percentage
            adjusted_share = base_share * snap_pct
            target_distribution[player_name] = adjusted_share
        
        # Normalize to ensure shares sum to reasonable total
        total_share = sum(target_distribution.values())
        if total_share > 0:
            for player in target_distribution:
                target_distribution[player] /= total_share
                target_distribution[player] *= 0.85  # Account for other targets
        
        constraints[f"{team}_{game_id}"] = {
            'estimated_targets': estimated_targets,
            'target_distribution': target_distribution
        }
    
    return constraints


def validate_reconciliation(original_projections: pd.DataFrame,
                          adjusted_projections: pd.DataFrame,
                          team_constraints: pd.DataFrame,
                          tolerance: float = 0.15) -> Dict[str, Any]:
    """
    Validate that reconciliation stayed within acceptable bounds.
    
    Args:
        original_projections: Original player projections
        adjusted_projections: Reconciled projections
        team_constraints: Team-level constraints
        tolerance: Maximum allowed deviation
        
    Returns:
        Validation report dictionary
    """
    report = {
        'passed': True,
        'warnings': [],
        'errors': [],
        'adjustments_summary': {}
    }
    
    # Check individual player adjustments
    for idx in original_projections.index:
        player_name = original_projections.loc[idx, 'player_name']
        
        for col in ['pass_yds_proj', 'rush_yds_proj', 'rec_yds_proj']:
            if col in original_projections.columns:
                orig_val = original_projections.loc[idx, col]
                adj_val = adjusted_projections.loc[idx, col]
                
                if orig_val > 0:
                    pct_change = abs(adj_val - orig_val) / orig_val
                    
                    if pct_change > tolerance:
                        report['warnings'].append(
                            f"{player_name} {col}: {pct_change:.1%} change (>{tolerance:.1%})"
                        )
                        
                    if pct_change > tolerance * 2:
                        report['errors'].append(
                            f"{player_name} {col}: {pct_change:.1%} change (>{tolerance*2:.1%})"
                        )
                        report['passed'] = False
    
    # Check team-level constraint satisfaction
    for (team, game_id), team_group in adjusted_projections.groupby(['team', 'game_id']):
        constraint = team_constraints[
            (team_constraints['team'] == team) & 
            (team_constraints['game_id'] == game_id)
        ]
        
        if constraint.empty:
            continue
            
        constraint_row = constraint.iloc[0]
        
        # Check passing yards constraint
        if 'pass_yds_implied' in constraint_row:
            qb_total = team_group[team_group['position'] == 'QB']['pass_yds_proj'].sum()
            target = constraint_row['pass_yds_implied']
            
            if target > 0:
                deviation = abs(qb_total - target) / target
                if deviation > tolerance:
                    report['warnings'].append(
                        f"{team} passing yards: {deviation:.1%} from constraint"
                    )
    
    return report
